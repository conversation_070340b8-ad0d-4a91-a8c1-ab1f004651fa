diff --git a/searchsvr/integrate/localcache/store_polygon_localcache.go b/searchsvr/integrate/localcache/store_polygon_localcache.go
index 91816695d..1f2c085b8 100644
--- a/searchsvr/integrate/localcache/store_polygon_localcache.go
+++ b/searchsvr/integrate/localcache/store_polygon_localcache.go
@@ -111,13 +111,13 @@ func (s *StoreCacheSys) GetStorePolygonFetchFunction(ctx context.Context, keyLis
 }
 
 func MarshalPolygon(polygon *polygon.Polygon) []byte {
-	if polygon == nil || len(polygon.Points) == 0 {
+	if polygon == nil || len(polygon.Latitudes) == 0 {
 		return []byte{}
 	}
-	bytes := make([]byte, 0, len(polygon.Points)*16)
-	for _, point := range polygon.Points {
-		latBytes := Float64ToByte(point.Lat)
-		lngBytes := Float64ToByte(point.Lng)
+	bytes := make([]byte, 0, len(polygon.Latitudes)*16)
+	for i := 0; i < len(polygon.Latitudes); i++ {
+		latBytes := Float64ToByte(polygon.Latitudes[i])
+		lngBytes := Float64ToByte(polygon.Longitudes[i])
 		bytes = append(bytes, latBytes...)
 		bytes = append(bytes, lngBytes...)
 	}
@@ -127,18 +127,20 @@ func UmMarshalPolygon(bytes []byte) *polygon.Polygon {
 	if len(bytes)%16 != 0 {
 		return nil
 	}
-	points := make([]*polygon.Point, 0, 0)
 	i := 0
+
+	latitudes := make([]float64, 0, 32)
+	longitudes := make([]float64, 0, 32)
+
 	for i+16 <= len(bytes) {
 		lat := ByteToFloat64(bytes[i : i+8])
 		lng := ByteToFloat64(bytes[i+8 : i+16])
-		points = append(points, &polygon.Point{
-			Lat: lat,
-			Lng: lng,
-		})
+		latitudes = append(latitudes, lat)
+		longitudes = append(longitudes, lng)
+
 		i += 16
 	}
-	return &polygon.Polygon{Points: points}
+	return &polygon.Polygon{Latitudes: latitudes, Longitudes: longitudes}
 }
 
 func Float64ToByte(float float64) []byte {
diff --git a/searchsvr/model/polygon/geo_polygon.go b/searchsvr/model/polygon/geo_polygon.go
index ee32b3d0d..c9879f917 100644
--- a/searchsvr/model/polygon/geo_polygon.go
+++ b/searchsvr/model/polygon/geo_polygon.go
@@ -6,50 +6,44 @@ import (
 )
 
 type Polygon struct {
-	Points []*Point `json:"Points"`
-}
-
-func NewPolygon(points []*Point) *Polygon {
-	return &Polygon{Points: points}
+	Latitudes  []float64 `json:"Latitudes"`
+	Longitudes []float64 `json:"Longitudes"`
 }
 
 func (p *Polygon) String() string {
 	str := "{"
-	for _, point := range p.Points {
-		str = str + fmt.Sprintf("%v", point)
+	for i := 0; i < len(p.Latitudes); i++ {
+		str = str + fmt.Sprintf("%v,%v;", p.Latitudes[i], p.Longitudes[i])
 	}
 	str = str + "}"
 	return str
 }
 
-func (p *Polygon) GetPoints() []*Point {
-	return p.Points
-}
-
-func (p *Polygon) Add(point *Point) {
-	p.Points = append(p.Points, point)
+func (p *Polygon) Add(latitude float64, longitude float64) {
+	p.Latitudes = append(p.Latitudes, latitude)
+	p.Longitudes = append(p.Longitudes, longitude)
 }
 
 func (p *Polygon) IsClosed() bool {
-	if len(p.Points) < 3 {
+	if len(p.Latitudes) < 3 {
 		return false
 	}
 
 	return true
 }
 
-func (p *Polygon) Contains(point *Point) bool {
+func (p *Polygon) Contains(pointLatitude, pointLongitude float64) bool {
 	if !p.IsClosed() {
 		return false
 	}
 
-	start := len(p.Points) - 1
+	start := len(p.Latitudes) - 1
 	end := 0
 
-	contains := p.intersectsWithRaycast(point, p.Points[start], p.Points[end])
+	contains := p.intersectsWithRaycast(pointLatitude, pointLongitude, p.Latitudes[start], p.Longitudes[start], p.Latitudes[end], p.Longitudes[end])
 
-	for i := 1; i < len(p.Points); i++ {
-		if p.intersectsWithRaycast(point, p.Points[i-1], p.Points[i]) {
+	for i := 1; i < len(p.Latitudes); i++ {
+		if p.intersectsWithRaycast(pointLatitude, pointLongitude, p.Latitudes[i-1], p.Longitudes[i-1], p.Latitudes[i], p.Longitudes[i]) {
 			contains = !contains
 		}
 	}
@@ -57,37 +51,38 @@ func (p *Polygon) Contains(point *Point) bool {
 	return contains
 }
 
-func (p *Polygon) intersectsWithRaycast(point *Point, start *Point, end *Point) bool {
-	if start.Lng > end.Lng {
-		start, end = end, start
+func (p *Polygon) intersectsWithRaycast(pointLatitude, pointLongitude float64, startLatitude, startLongitude float64, endLatitude, endLongitude float64) bool {
+	if startLongitude > endLongitude {
+		startLatitude, endLatitude = endLatitude, startLatitude
+		startLongitude, endLongitude = endLongitude, startLongitude
 	}
-	for point.Lng == start.Lng || point.Lng == end.Lng {
-		newLng := math.Nextafter(point.Lng, math.Inf(1))
-		point = NewPoint(point.Lat, newLng)
+	for pointLongitude == startLongitude || pointLongitude == endLongitude {
+		newLng := math.Nextafter(pointLongitude, math.Inf(1))
+		pointLongitude = newLng
 	}
-	if point.Lng < start.Lng || point.Lng > end.Lng {
+	if pointLongitude < startLongitude || pointLongitude > endLongitude {
 		return false
 	}
 
-	if start.Lat > end.Lat {
-		if point.Lat > start.Lat {
+	if startLatitude > endLatitude {
+		if pointLatitude > startLatitude {
 			return false
 		}
-		if point.Lat < end.Lat {
+		if pointLatitude < endLatitude {
 			return true
 		}
 
 	} else {
-		if point.Lat > end.Lat {
+		if pointLatitude > endLatitude {
 			return false
 		}
-		if point.Lat < start.Lat {
+		if pointLatitude < startLatitude {
 			return true
 		}
 	}
 
-	raySlope := (point.Lng - start.Lng) / (point.Lat - start.Lat)
-	diagSlope := (end.Lng - start.Lng) / (end.Lat - start.Lat)
+	raySlope := (pointLongitude - startLongitude) / (pointLatitude - startLatitude)
+	diagSlope := (endLongitude - startLongitude) / (endLatitude - startLatitude)
 
 	return raySlope >= diagSlope
 }
diff --git a/searchsvr/model/polygon/polygon_point.go b/searchsvr/model/polygon/polygon_point.go
deleted file mode 100644
index 2462c4a45..000000000
--- a/searchsvr/model/polygon/polygon_point.go
+++ /dev/null
@@ -1,26 +0,0 @@
-package polygon
-
-import (
-	"fmt"
-)
-
-type Point struct {
-	Lat float64 `json:"Lat"`
-	Lng float64 `json:"Lng"`
-}
-
-func NewPoint(lat float64, lng float64) *Point {
-	return &Point{Lat: lat, Lng: lng}
-}
-
-func (p *Point) GetLat() float64 {
-	return p.Lat
-}
-
-func (p *Point) GetLng() float64 {
-	return p.Lng
-}
-
-func (p *Point) String() string {
-	return fmt.Sprintf("[%v:%v]", p.Lng, p.Lat)
-}
diff --git a/searchsvr/model/polygon/polygon_util.go b/searchsvr/model/polygon/polygon_util.go
index 124451526..527953bf2 100644
--- a/searchsvr/model/polygon/polygon_util.go
+++ b/searchsvr/model/polygon/polygon_util.go
@@ -48,11 +48,10 @@ func BuildPolygon(ctx context.Context, rsp *o2oalgo_datamanagement.GetDataRsp) (
 					data = strings.TrimSpace(data)
 					dataVec := strings.Split(data, " ")
 					if len(dataVec) == 2 {
-						dataOne, err1 := strconv.ParseFloat(dataVec[0], 64)
-						dataTwo, err2 := strconv.ParseFloat(dataVec[1], 64)
+						latitude, err1 := strconv.ParseFloat(dataVec[0], 64)  // latitude
+						longitude, err2 := strconv.ParseFloat(dataVec[1], 64) // longitude
 						if err1 == nil && err2 == nil {
-							point := NewPoint(dataTwo, dataOne)
-							pg.Add(point)
+							pg.Add(latitude, longitude)
 						} else {
 							logkit.FromContext(ctx).Error("polygon data, strconv failed", logkit.Any("err1", err1), logkit.Any("err2", err2))
 						}
diff --git a/searchsvr/processor/filter/filter_distance.go b/searchsvr/processor/filter/filter_distance.go
index 7ae334bd3..646098ddb 100644
--- a/searchsvr/processor/filter/filter_distance.go
+++ b/searchsvr/processor/filter/filter_distance.go
@@ -16,7 +16,6 @@ import (
 	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
 	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
 	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
-	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/polygon"
 )
 
 func StorePolygonDistanceFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
@@ -39,11 +38,10 @@ func StorePolygonDistanceFilter(ctx context.Context, traceInfo *traceinfo.TraceI
 		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFilterPolygonDistance, time.Since(pt))
 	}()
 	resStoreIDs := make([]*model.StoreInfo, 0, len(storeInfos))
-	var locPoint = polygon.NewPoint(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude)
 	storePolygonFilterTime := time.Now()
 	for _, store := range storeInfos {
 		// 多边形数据异常
-		if isActiveByStoreDistance(traceInfo, store, locPoint) || isActiveByStorePickupDistance(traceInfo, store, locPoint) {
+		if isActiveByStoreDistance(traceInfo, store, traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude) || isActiveByStorePickupDistance(traceInfo, store, traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude) {
 			resStoreIDs = append(resStoreIDs, store)
 		} else {
 			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStoreDistance, store.Distance)
@@ -62,7 +60,7 @@ func StorePolygonDistanceFilter(ctx context.Context, traceInfo *traceinfo.TraceI
 // 2.自提召回
 //
 //	2.1 全部用自提距离
-func isActiveByStoreDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, locPoint *polygon.Point) bool {
+func isActiveByStoreDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, locPointLatitude, locPointLongitude float64) bool {
 	// 带 self pickup filter 或者仅自提商家召回的，不使用商家配送距离过滤
 	if (env.GetCID() == cid.TH || env.GetCID() == cid.MY) && (decision.IsSelfPickupFilter(traceInfo) || store.IsOnlySelfPickupRecall()) {
 		return false
@@ -82,7 +80,7 @@ func isActiveByStoreDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreI
 		}
 		return true
 	}
-	if !(store.StorePolygon.Contains(locPoint)) {
+	if !(store.StorePolygon.Contains(locPointLatitude, locPointLongitude)) {
 		return false
 	}
 	// 这个20公里是必须的隐形条件
@@ -92,7 +90,7 @@ func isActiveByStoreDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreI
 	return true
 }
 
-func isActiveByStorePickupDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, locPoint *polygon.Point) bool {
+func isActiveByStorePickupDistance(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, locPointLatitude, locPointLongitude float64) bool {
 	// 不满足自提商家召回条件，直接跳过
 	if !(env.GetCID() == cid.TH || env.GetCID() == cid.MY) {
 		return false
@@ -113,7 +111,7 @@ func isActiveByStorePickupDistance(traceInfo *traceinfo.TraceInfo, store *model.
 			}
 			return true
 		}
-		if !(store.StorePickupPolygon.Contains(locPoint)) {
+		if !(store.StorePickupPolygon.Contains(locPointLatitude, locPointLongitude)) {
 			return false
 		}
 		// 这个100公里是必须的隐形条件
